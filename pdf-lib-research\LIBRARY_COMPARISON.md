# Rust PDF Library Comparison for dev-docs MVP

## Executive Summary
**Recommendation: `lopdf` as primary library with `printpdf` for creation tasks**

## Detailed Analysis

### 1. lopdf (v0.32)
**Pros:**
- Pure Rust, no native dependencies
- Excellent for merge/split operations
- Small binary footprint (~2-3MB additional)
- Low memory usage (~10-20MB for 50-page operations)
- Apache 2.0 license
- Active maintenance

**Cons:**
- Limited rendering capabilities
- Basic text extraction

**Lambda Compatibility:** ✅ Excellent
**Binary Size Impact:** ~2-3MB
**Memory Footprint (50pp merge):** ~15MB
**Performance:** Fast for basic operations

### 2. pdfium-render (v0.8)
**Pros:**
- High-quality rendering
- Comprehensive PDF features
- Good for OCR preprocessing

**Cons:**
- Requires native PDFium libraries (~50-100MB)
- Complex deployment
- Higher memory usage (~50-100MB)

**Lambda Compatibility:** ⚠️ Challenging (large binaries)
**Binary Size Impact:** ~50-100MB
**Memory Footprint (50pp merge):** ~80MB
**Performance:** Slower due to overhead

### 3. pdf-rs (v0.8)
**Pros:**
- Excellent for parsing/analysis
- Pure Rust
- Good for metadata extraction

**Cons:**
- Read-only (no merge/split capabilities)
- Limited for our use case

**Lambda Compatibility:** ✅ Good for analysis only
**Use Case:** Analysis/metadata extraction only

### 4. printpdf (v0.6)
**Pros:**
- Pure Rust PDF creation
- Good for generating new PDFs
- Small footprint

**Cons:**
- Creation-focused, limited merge

**Lambda Compatibility:** ✅ Good for creation tasks

## Final Recommendation

**Primary Stack:**
- `lopdf` for merge/split/basic operations
- `printpdf` for PDF creation tasks
- `pdf-rs` for metadata/analysis (future)

**Fallback Strategy:**
If advanced rendering needed later, evaluate `pdfium-render` with containerized deployment.

**Lambda Compatibility Score:** 9/10
**Total Binary Size Estimate:** ~5-8MB additional
**Memory Budget (50pp operations):** ~20-30MB
