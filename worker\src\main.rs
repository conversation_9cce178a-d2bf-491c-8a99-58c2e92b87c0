mod pdf_processor;

use anyhow::Result;
use axum::{
    extract::{<PERSON><PERSON>, Path},
    http::StatusCode,
    response::<PERSON><PERSON> as ResponseJson,
    routing::{get, post},
    Router,
};
use pdf_processor::{PdfProcessor, PdfInfo};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::RwLock;
use std::sync::Arc;
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize)]
pub struct JobRequest {
    pub operation: String,
    pub input_files: Vec<String>,
    pub options: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct JobResponse {
    pub job_id: String,
    pub status: String,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct JobStatus {
    pub job_id: String,
    pub status: String,
    pub progress: f32,
    pub result_url: Option<String>,
    pub error: Option<String>,
}

// In-memory job store (replace with Redis/DynamoDB in production)
type JobStore = Arc<RwLock<HashMap<String, JobStatus>>>;

#[tokio::main]
async fn main() -> Result<()> {
    let job_store: JobStore = Arc::new(RwLock::new(HashMap::new()));
    
    let app = Router::new()
        .route("/healthz", get(health_check))
        .route("/jobs", post(create_job))
        .route("/jobs/:job_id", get(get_job_status))
        .with_state(job_store);

    println!("🚀 dev-docs worker starting on http://0.0.0.0:8080");
    
    let listener = tokio::net::TcpListener::bind("0.0.0.0:8080").await?;
    axum::serve(listener, app).await?;
    
    Ok(())
}

async fn health_check() -> ResponseJson<serde_json::Value> {
    ResponseJson(serde_json::json!({
        "status": "healthy",
        "service": "dev-docs-worker",
        "version": "0.1.0"
    }))
}

async fn create_job(
    axum::extract::State(store): axum::extract::State<JobStore>,
    Json(request): Json<JobRequest>,
) -> Result<(StatusCode, ResponseJson<JobResponse>), StatusCode> {
    let job_id = Uuid::new_v4().to_string();
    
    // Initialize job status
    let job_status = JobStatus {
        job_id: job_id.clone(),
        status: "accepted".to_string(),
        progress: 0.0,
        result_url: None,
        error: None,
    };
    
    // Store job
    {
        let mut jobs = store.write().await;
        jobs.insert(job_id.clone(), job_status);
    }
    
    // Process job asynchronously
    let store_clone = store.clone();
    tokio::spawn(async move {
        if let Err(e) = process_job_async(&job_id, request, store_clone).await {
            println!("Job {} failed: {}", job_id, e);
            // Update job status to failed
            let mut jobs = store_clone.write().await;
            if let Some(job) = jobs.get_mut(&job_id) {
                job.status = "failed".to_string();
                job.error = Some(e.to_string());
            }
        }
    });
    
    let response = JobResponse {
        job_id,
        status: "accepted".to_string(),
        message: "Job queued for processing".to_string(),
    };
    
    Ok((StatusCode::ACCEPTED, ResponseJson(response)))
}

async fn get_job_status(
    axum::extract::State(store): axum::extract::State<JobStore>,
    Path(job_id): Path<String>,
) -> Result<ResponseJson<JobStatus>, StatusCode> {
    let jobs = store.read().await;
    
    match jobs.get(&job_id) {
        Some(status) => Ok(ResponseJson(status.clone())),
        None => Err(StatusCode::NOT_FOUND),
    }
}

async fn process_job_async(
    job_id: &str,
    request: JobRequest,
    store: JobStore,
) -> Result<()> {
    let processor = PdfProcessor::new();
    
    // Update status to processing
    {
        let mut jobs = store.write().await;
        if let Some(job) = jobs.get_mut(job_id) {
            job.status = "processing".to_string();
            job.progress = 10.0;
        }
    }
    
    let result = match request.operation.as_str() {
        "merge" => {
            println!("Merging PDFs: {:?}", request.input_files);
            let merged_pdf = processor.merge_pdfs(&request.input_files).await?;
            
            // In production, upload to S3 and return pre-signed URL
            let output_path = format!("/tmp/merged_{}.pdf", job_id);
            tokio::fs::write(&output_path, merged_pdf).await?;
            
            Some(format!("file://{}", output_path))
        },
        "split" => {
            if request.input_files.len() != 1 {
                return Err(anyhow::anyhow!("Split operation requires exactly one input file"));
            }
            
            println!("Splitting PDF: {}", request.input_files[0]);
            let split_pdfs = processor.split_pdf(&request.input_files[0]).await?;
            
            // Save split files
            for (i, pdf_data) in split_pdfs.iter().enumerate() {
                let output_path = format!("/tmp/split_{}_{}.pdf", job_id, i + 1);
                tokio::fs::write(&output_path, pdf_data).await?;
            }
            
            Some(format!("Split into {} files in /tmp/", split_pdfs.len()))
        },
        "compress" => {
            if request.input_files.len() != 1 {
                return Err(anyhow::anyhow!("Compress operation requires exactly one input file"));
            }
            
            println!("Compressing PDF: {}", request.input_files[0]);
            let compressed_pdf = processor.compress_pdf(&request.input_files[0]).await?;
            
            let output_path = format!("/tmp/compressed_{}.pdf", job_id);
            tokio::fs::write(&output_path, compressed_pdf).await?;
            
            Some(format!("file://{}", output_path))
        },
        "info" => {
            if request.input_files.len() != 1 {
                return Err(anyhow::anyhow!("Info operation requires exactly one input file"));
            }
            
            println!("Getting PDF info: {}", request.input_files[0]);
            let info = processor.get_pdf_info(&request.input_files[0]).await?;
            
            Some(format!("Pages: {}, Title: {:?}, Version: {}", 
                info.page_count, info.title, info.version))
        },
        _ => return Err(anyhow::anyhow!("Unknown operation: {}", request.operation)),
    };
    
    // Update status to completed
    {
        let mut jobs = store.write().await;
        if let Some(job) = jobs.get_mut(job_id) {
            job.status = "completed".to_string();
            job.progress = 100.0;
            job.result_url = result;
        }
    }
    
    println!("✅ Job {} completed successfully", job_id);
    Ok(())
}
