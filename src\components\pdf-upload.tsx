'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, X, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface UploadedFile {
  id: string;
  file: File;
  status: 'pending' | 'uploading' | 'uploaded' | 'error';
  progress: number;
  url?: string;
}

interface JobStatus {
  job_id: string;
  status: 'accepted' | 'processing' | 'completed' | 'failed';
  progress: number;
  result_url?: string;
  error?: string;
}

export default function PdfUpload() {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [operation, setOperation] = useState<'merge' | 'split' | 'compress' | 'info'>('merge');
  const [isProcessing, setIsProcessing] = useState(false);
  const [jobStatus, setJobStatus] = useState<JobStatus | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      status: 'pending' as const,
      progress: 0,
    }));
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    multiple: operation === 'merge',
  });

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id));
  };

  const processFiles = async () => {
    if (files.length === 0) return;

    setIsProcessing(true);
    setJobStatus(null);

    try {
      // In a real implementation, we'd upload files to S3 first
      // For now, we'll simulate with local file paths
      const filePaths = files.map(f => `/tmp/${f.file.name}`);

      const response = await fetch('/api/jobs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          operation,
          input_files: filePaths,
          options: {},
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create job');
      }

      const jobResponse = await response.json();
      setJobStatus({
        job_id: jobResponse.job_id,
        status: 'accepted',
        progress: 0,
      });

      // Poll for job status
      pollJobStatus(jobResponse.job_id);
    } catch (error) {
      console.error('Error processing files:', error);
      setJobStatus({
        job_id: '',
        status: 'failed',
        progress: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const pollJobStatus = async (jobId: string) => {
    const poll = async () => {
      try {
        const response = await fetch(`/api/jobs/${jobId}`);
        if (!response.ok) return;

        const status: JobStatus = await response.json();
        setJobStatus(status);

        if (status.status === 'processing' || status.status === 'accepted') {
          setTimeout(poll, 1000); // Poll every second
        }
      } catch (error) {
        console.error('Error polling job status:', error);
      }
    };

    poll();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'accepted':
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'Job accepted, waiting to start...';
      case 'processing':
        return 'Processing your PDF...';
      case 'completed':
        return 'Processing complete!';
      case 'failed':
        return 'Processing failed';
      default:
        return '';
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
      {/* Operation Selection */}
      <div className="space-y-3">
        <h3 className="text-lg font-semibold text-white">Choose Operation</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {[
            { key: 'merge', label: 'Merge PDFs', desc: 'Combine multiple PDFs' },
            { key: 'split', label: 'Split PDF', desc: 'Extract individual pages' },
            { key: 'compress', label: 'Compress PDF', desc: 'Reduce file size' },
            { key: 'info', label: 'PDF Info', desc: 'Get document details' },
          ].map(({ key, label, desc }) => (
            <button
              key={key}
              onClick={() => setOperation(key as any)}
              className={cn(
                'p-4 rounded-xl border-2 text-left transition-all duration-200',
                'hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500',
                operation === key
                  ? 'border-blue-500 bg-blue-500/10 text-blue-400'
                  : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'
              )}
            >
              <div className="font-medium">{label}</div>
              <div className="text-sm opacity-70">{desc}</div>
            </button>
          ))}
        </div>
      </div>

      {/* File Upload Area */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-white">Upload PDF Files</h3>
        <div
          {...getRootProps()}
          className={cn(
            'border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200',
            'hover:border-blue-500 hover:bg-blue-500/5 focus:outline-none focus:ring-2 focus:ring-blue-500',
            isDragActive
              ? 'border-blue-500 bg-blue-500/10'
              : 'border-gray-700 bg-gray-800/30'
          )}
        >
          <input {...getInputProps()} />
          <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-lg font-medium text-white mb-2">
            {isDragActive ? 'Drop PDFs here...' : 'Drag & drop PDF files here'}
          </p>
          <p className="text-gray-400">
            or click to browse • {operation === 'merge' ? 'Multiple files allowed' : 'Single file only'}
          </p>
        </div>
      </div>

      {/* Uploaded Files */}
      {files.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-medium text-white">Uploaded Files ({files.length})</h4>
          <div className="space-y-2">
            {files.map((file) => (
              <div
                key={file.id}
                className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg border border-gray-700"
              >
                <div className="flex items-center space-x-3">
                  <FileText className="h-5 w-5 text-red-400" />
                  <div>
                    <p className="font-medium text-white">{file.file.name}</p>
                    <p className="text-sm text-gray-400">
                      {(file.file.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => removeFile(file.id)}
                  className="p-1 hover:bg-gray-700 rounded transition-colors"
                >
                  <X className="h-4 w-4 text-gray-400" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Process Button */}
      {files.length > 0 && (
        <button
          onClick={processFiles}
          disabled={isProcessing}
          className={cn(
            'w-full py-4 px-6 rounded-xl font-semibold text-white transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-blue-500',
            isProcessing
              ? 'bg-gray-600 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 hover:scale-105'
          )}
        >
          {isProcessing ? (
            <div className="flex items-center justify-center space-x-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Processing...</span>
            </div>
          ) : (
            `${operation.charAt(0).toUpperCase() + operation.slice(1)} PDF${files.length > 1 ? 's' : ''}`
          )}
        </button>
      )}

      {/* Job Status */}
      {jobStatus && (
        <div className="p-4 bg-gray-800/50 rounded-xl border border-gray-700">
          <div className="flex items-center space-x-3 mb-3">
            {getStatusIcon(jobStatus.status)}
            <div>
              <p className="font-medium text-white">{getStatusText(jobStatus.status)}</p>
              <p className="text-sm text-gray-400">Job ID: {jobStatus.job_id}</p>
            </div>
          </div>

          {jobStatus.progress > 0 && (
            <div className="mb-3">
              <div className="flex justify-between text-sm text-gray-400 mb-1">
                <span>Progress</span>
                <span>{Math.round(jobStatus.progress)}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${jobStatus.progress}%` }}
                />
              </div>
            </div>
          )}

          {jobStatus.result_url && (
            <div className="mt-3">
              <a
                href={jobStatus.result_url}
                className="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Download Result
              </a>
            </div>
          )}

          {jobStatus.error && (
            <div className="mt-3 p-3 bg-red-900/20 border border-red-700 rounded-lg">
              <p className="text-red-400 text-sm">{jobStatus.error}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
