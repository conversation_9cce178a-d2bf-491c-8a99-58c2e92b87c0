use anyhow::Result;
use std::time::Instant;

fn main() -> Result<()> {
    println!("=== PDF-RS Library Test ===");
    
    // Note: pdf-rs is primarily a PDF parser, not a writer
    // It's excellent for reading/analyzing PDFs but limited for merge operations
    
    println!("✓ pdf-rs is mainly for parsing/reading PDFs");
    println!("Note: Limited merge capabilities - better suited for analysis tasks");
    
    Ok(())
}
