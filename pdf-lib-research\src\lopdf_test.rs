use anyhow::Result;
use lopdf::{Document, Object, Stream};
use std::collections::BTreeMap;
use std::time::Instant;

fn main() -> Result<()> {
    println!("=== LOPDF Library Test ===");
    
    // Create a sample PDF with ~50 pages for testing
    let doc = create_sample_pdf(50)?;
    
    // Save to file for merge testing
    doc.save("sample_50_pages.pdf")?;
    println!("✓ Created 50-page sample PDF");
    
    // Test merge operation
    let start = Instant::now();
    let merged = merge_pdfs(&["sample_50_pages.pdf", "sample_50_pages.pdf"])?;
    let merge_duration = start.elapsed();
    
    merged.save("merged_100_pages.pdf")?;
    println!("✓ Merged 2x50 pages in {:?}", merge_duration);
    
    // Memory footprint estimation
    let file_size = std::fs::metadata("merged_100_pages.pdf")?.len();
    println!("✓ Output file size: {} KB", file_size / 1024);
    
    // Binary size will be checked after compilation
    println!("✓ lopdf test completed successfully");
    
    Ok(())
}

fn create_sample_pdf(pages: usize) -> Result<Document> {
    let mut doc = Document::with_version("1.5");
    let pages_id = doc.new_object_id();
    let font_id = doc.new_object_id();
    
    // Add font
    let font = lopdf::dictionary! {
        "Type" => "Font",
        "Subtype" => "Type1",
        "BaseFont" => "Helvetica",
    };
    doc.objects.insert(font_id, Object::Dictionary(font));
    
    let mut page_ids = Vec::new();
    
    for i in 0..pages {
        let page_id = doc.new_object_id();
        let content_id = doc.new_object_id();
        
        // Create page content
        let content = format!("BT /F1 12 Tf 100 700 Td (Page {}) Tj ET", i + 1);
        let content_stream = Stream::new(lopdf::dictionary! {}, content.into_bytes());
        doc.objects.insert(content_id, Object::Stream(content_stream));
        
        // Create page
        let page = lopdf::dictionary! {
            "Type" => "Page",
            "Parent" => pages_id,
            "Contents" => content_id,
            "MediaBox" => vec![0.into(), 0.into(), 612.into(), 792.into()],
            "Resources" => lopdf::dictionary! {
                "Font" => lopdf::dictionary! {
                    "F1" => font_id,
                },
            },
        };
        doc.objects.insert(page_id, Object::Dictionary(page));
        page_ids.push(page_id.into());
    }
    
    // Create pages object
    let pages = lopdf::dictionary! {
        "Type" => "Pages",
        "Kids" => page_ids,
        "Count" => pages,
    };
    doc.objects.insert(pages_id, Object::Dictionary(pages));
    
    // Create catalog
    let catalog_id = doc.new_object_id();
    let catalog = lopdf::dictionary! {
        "Type" => "Catalog",
        "Pages" => pages_id,
    };
    doc.objects.insert(catalog_id, Object::Dictionary(catalog));
    doc.trailer.set("Root", catalog_id);
    
    Ok(doc)
}

fn merge_pdfs(paths: &[&str]) -> Result<Document> {
    let mut merged = Document::with_version("1.5");
    
    for path in paths {
        let doc = Document::load(path)?;
        merged = merged.prune_objects();
        
        // Simple merge - in production we'd need more sophisticated page merging
        for (id, object) in doc.objects {
            merged.objects.insert(merged.new_object_id(), object);
        }
    }
    
    Ok(merged)
}
