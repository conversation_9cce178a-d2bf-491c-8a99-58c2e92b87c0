use anyhow::{Result, anyhow};
use lopdf::{Document, Object, ObjectId};
use std::collections::BTreeMap;
use std::path::Path;
use uuid::Uuid;

pub struct PdfProcessor;

impl PdfProcessor {
    pub fn new() -> Self {
        Self
    }

    /// Merge multiple PDF files into a single document
    pub async fn merge_pdfs(&self, input_paths: &[String]) -> Result<Vec<u8>> {
        if input_paths.is_empty() {
            return Err(anyhow!("No input files provided"));
        }

        let mut merged_doc = Document::with_version("1.7");
        let mut page_objects = Vec::new();
        let mut all_objects = BTreeMap::new();
        
        // Process each input PDF
        for path in input_paths {
            let doc = Document::load(path)
                .map_err(|e| anyhow!("Failed to load PDF {}: {}", path, e))?;
            
            // Get pages from this document
            let pages = doc.get_pages();
            
            for (page_num, page_id) in pages {
                if let Ok(page_obj) = doc.get_object(page_id) {
                    // Clone the page and all referenced objects
                    let new_page_id = merged_doc.new_object_id();
                    
                    // Deep copy the page object and its resources
                    if let Object::Dictionary(ref page_dict) = page_obj {
                        let mut new_page_dict = page_dict.clone();
                        
                        // Update parent reference (will be set later)
                        new_page_dict.remove(b"Parent");
                        
                        merged_doc.objects.insert(new_page_id, Object::Dictionary(new_page_dict));
                        page_objects.push(new_page_id);
                    }
                }
            }
        }

        // Create pages object
        let pages_id = merged_doc.new_object_id();
        let pages_dict = lopdf::dictionary! {
            "Type" => "Pages",
            "Kids" => page_objects.iter().map(|&id| Object::Reference(id)).collect::<Vec<_>>(),
            "Count" => page_objects.len() as i64,
        };
        merged_doc.objects.insert(pages_id, Object::Dictionary(pages_dict));

        // Update all pages to reference the new parent
        for &page_id in &page_objects {
            if let Some(Object::Dictionary(ref mut page_dict)) = merged_doc.objects.get_mut(&page_id) {
                page_dict.set("Parent", pages_id);
            }
        }

        // Create catalog
        let catalog_id = merged_doc.new_object_id();
        let catalog_dict = lopdf::dictionary! {
            "Type" => "Catalog",
            "Pages" => pages_id,
        };
        merged_doc.objects.insert(catalog_id, Object::Dictionary(catalog_dict));
        merged_doc.trailer.set("Root", catalog_id);

        // Save to bytes
        let mut output = Vec::new();
        merged_doc.save_to(&mut output)
            .map_err(|e| anyhow!("Failed to save merged PDF: {}", e))?;

        Ok(output)
    }

    /// Split a PDF into individual pages
    pub async fn split_pdf(&self, input_path: &str) -> Result<Vec<Vec<u8>>> {
        let doc = Document::load(input_path)
            .map_err(|e| anyhow!("Failed to load PDF {}: {}", input_path, e))?;
        
        let pages = doc.get_pages();
        let mut split_pdfs = Vec::new();

        for (page_num, page_id) in pages {
            // Create a new document with just this page
            let mut single_page_doc = Document::with_version("1.7");
            
            // Copy the page and its resources
            if let Ok(page_obj) = doc.get_object(page_id) {
                let new_page_id = single_page_doc.new_object_id();
                
                if let Object::Dictionary(ref page_dict) = page_obj {
                    let mut new_page_dict = page_dict.clone();
                    new_page_dict.remove(b"Parent");
                    
                    single_page_doc.objects.insert(new_page_id, Object::Dictionary(new_page_dict));
                    
                    // Create pages object for single page
                    let pages_id = single_page_doc.new_object_id();
                    let pages_dict = lopdf::dictionary! {
                        "Type" => "Pages",
                        "Kids" => vec![Object::Reference(new_page_id)],
                        "Count" => 1,
                    };
                    single_page_doc.objects.insert(pages_id, Object::Dictionary(pages_dict));
                    
                    // Update page parent
                    if let Some(Object::Dictionary(ref mut page_dict)) = single_page_doc.objects.get_mut(&new_page_id) {
                        page_dict.set("Parent", pages_id);
                    }
                    
                    // Create catalog
                    let catalog_id = single_page_doc.new_object_id();
                    let catalog_dict = lopdf::dictionary! {
                        "Type" => "Catalog",
                        "Pages" => pages_id,
                    };
                    single_page_doc.objects.insert(catalog_id, Object::Dictionary(catalog_dict));
                    single_page_doc.trailer.set("Root", catalog_id);
                    
                    // Save to bytes
                    let mut output = Vec::new();
                    single_page_doc.save_to(&mut output)
                        .map_err(|e| anyhow!("Failed to save page {}: {}", page_num, e))?;
                    
                    split_pdfs.push(output);
                }
            }
        }

        Ok(split_pdfs)
    }

    /// Compress PDF by optimizing structure (basic implementation)
    pub async fn compress_pdf(&self, input_path: &str) -> Result<Vec<u8>> {
        let mut doc = Document::load(input_path)
            .map_err(|e| anyhow!("Failed to load PDF {}: {}", input_path, e))?;
        
        // Basic compression: prune unused objects
        doc = doc.prune_objects();
        
        // Compress streams (lopdf handles this automatically when saving)
        let mut output = Vec::new();
        doc.save_to(&mut output)
            .map_err(|e| anyhow!("Failed to save compressed PDF: {}", e))?;

        Ok(output)
    }

    /// Get PDF metadata and page count
    pub async fn get_pdf_info(&self, input_path: &str) -> Result<PdfInfo> {
        let doc = Document::load(input_path)
            .map_err(|e| anyhow!("Failed to load PDF {}: {}", input_path, e))?;
        
        let pages = doc.get_pages();
        let page_count = pages.len();
        
        // Try to extract title from document info
        let title = doc.trailer.get(b"Info")
            .and_then(|info_ref| doc.get_object(info_ref.as_reference().ok()?).ok())
            .and_then(|info_obj| {
                if let Object::Dictionary(ref info_dict) = info_obj {
                    info_dict.get(b"Title")
                        .and_then(|title_obj| title_obj.as_str().ok())
                        .map(|s| s.to_string())
                } else {
                    None
                }
            });

        Ok(PdfInfo {
            page_count,
            title,
            version: doc.version.clone(),
        })
    }
}

#[derive(Debug, serde::Serialize)]
pub struct PdfInfo {
    pub page_count: usize,
    pub title: Option<String>,
    pub version: String,
}
