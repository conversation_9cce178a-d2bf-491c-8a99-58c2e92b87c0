use anyhow::Result;
use pdfium_render::prelude::*;
use std::time::Instant;

fn main() -> Result<()> {
    println!("=== PDFIUM-RENDER Library Test ===");
    
    // Initialize Pdfium
    let pdfium = Pdfium::new(
        Pdfium::bind_to_library(Pdfium::pdfium_platform_library_name_at_path("./"))
            .or_else(|_| Pdfium::bind_to_system_library())
            .unwrap()
    );
    
    println!("✓ Pdfium initialized");
    
    // Test would require actual PDF files to merge
    // For now, we'll test basic functionality and measure binary size
    
    println!("✓ pdfium-render test completed (requires PDF files for full merge test)");
    println!("Note: This library excels at rendering but may be heavier for simple merge operations");
    
    Ok(())
}
