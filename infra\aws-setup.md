# AWS Infrastructure Setup for dev-docs MVP

## S3 Buckets Required

### 1. dev-docs-dev (Development/Testing)
- **Purpose:** Automated tests, development uploads
- **TTL:** No expiration (manual cleanup)
- **CORS:** Enabled for localhost:3000
- **Versioning:** Disabled

### 2. dev-docs-staging (Staging Environment)  
- **Purpose:** User uploads in staging
- **TTL:** 2 hours automatic deletion
- **CORS:** Enabled for staging domain
- **Versioning:** Disabled

### 3. dev-docs-prod (Future Production)
- **Purpose:** Production user uploads
- **TTL:** 2 hours automatic deletion
- **CORS:** Enabled for production domain
- **Versioning:** Disabled

## IAM Roles & Policies

### Lambda Execution Role
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject"
      ],
      "Resource": [
        "arn:aws:s3:::dev-docs-dev/*",
        "arn:aws:s3:::dev-docs-staging/*",
        "arn:aws:s3:::dev-docs-prod/*"
      ]
    },
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:*:*:*"
    }
  ]
}
```

### Gateway API Role (for pre-signed URLs)
```json
{
  "Version": "2012-10-17", 
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject"
      ],
      "Resource": [
        "arn:aws:s3:::dev-docs-dev/*",
        "arn:aws:s3:::dev-docs-staging/*"
      ]
    }
  ]
}
```

## Setup Commands

```bash
# Create buckets
aws s3 mb s3://dev-docs-dev --region us-east-1
aws s3 mb s3://dev-docs-staging --region us-east-1

# Set lifecycle policy for staging (2h TTL)
aws s3api put-bucket-lifecycle-configuration \
  --bucket dev-docs-staging \
  --lifecycle-configuration file://staging-lifecycle.json

# Enable CORS for both buckets
aws s3api put-bucket-cors \
  --bucket dev-docs-dev \
  --cors-configuration file://cors-config.json

aws s3api put-bucket-cors \
  --bucket dev-docs-staging \
  --cors-configuration file://cors-config.json
```
