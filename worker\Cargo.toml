[package]
name = "dev-docs-worker"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
uuid = { workspace = true }
aws-sdk-s3 = { workspace = true }
aws-config = { workspace = true }

# PDF processing
lopdf = "0.32"
printpdf = "0.6"

# HTTP server for local development
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors"] }

# Lambda runtime (for AWS deployment)
lambda_runtime = "0.8"
lambda_web = "0.2"

[features]
default = ["local"]
local = []
lambda = []
