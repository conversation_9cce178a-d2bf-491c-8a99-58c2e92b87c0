import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'dev-docs - Your All-in-One PDF Toolbox',
  description: 'Make every PDF task instant, free for core needs, and privacy-respecting. Merge, split, compress, and convert with blazing speed.',
  keywords: 'PDF, merge, split, compress, convert, free, privacy, tools',
  authors: [{ name: 'dev-docs' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#0D0D0D',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=Reem+Kufi+Ink:wght@400;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className={`${inter.className} antialiased bg-[#0D0D0D] text-white`}>
        {children}
      </body>
    </html>
  )
}