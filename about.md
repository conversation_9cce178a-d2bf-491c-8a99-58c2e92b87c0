# dev-docs – Context Brief for AI Agents
_Last updated: 2025-08-02_

---

## What dev-docs *is*

dev-docs is an **all-in-one, web-based PDF toolbox**.  
The “docs” in our name refers to **documents**, but the **initial and primary focus is PDF workflows** (merge, split, compress, convert, OCR, automate). Future expansions may tackle Word, images, and plain-text, yet every early decision should assume **“PDF first.”**

---

## Mission (single sentence)

> **Make every PDF task instant, free for core needs, and privacy-respecting—accessible to anyone, everywhere.**

---

## North-star capabilities (final product)

1. **Unlimited-size Smart Merge** – Handles multi-GB PDFs, auto-fixes rotation & bookmarks.  
2. **AI-Powered OCR & Search** – Local-first if supported, serverless GPU fallback; semantic search.  
3. **Workflow Automations** – No-code recipes: “Upload ➜ Merge ➜ Compress ➜ Email link.”

---

## MVP directives

| Area | Constraint |
|------|------------|
| **Backend language** | **Rust** for the MVP (speed + memory safety). Heavy workers built with `pdfcpu-rs` or FFI to C libraries. **Plan to migrate to Go** once performance envelope & scaling patterns are understood. |
| **Cost ceiling** | Build only with OSS that carries **$0 licensing & infra cost** (MIT / Apache-2.0). |
| **Feature scope** | Merge, Split, Compress, Rotate, Word→PDF, PDF→Images. File limit: **≤ 100 MB**. |
| **Infra** | Next.js 14 (App Router) frontend, server actions + Edge functions (TypeScript). Workers: Rust binaries in Lambda containers. Storage: AWS S3 (uploads/outputs, TTL 2 h), SQS (jobs), DynamoDB (metadata). |
| **Privacy** | Files auto-purge after 2 h; no watermarking; client-side encryption mode optional. |

---

## Values & principles

1. **Free-first** – Charge only where third-party costs are unavoidable.  
2. **Open-source** – Default to MIT/Apache-2.0; avoid AGPL for hosted services.  
3. **High speed** – Sub-second UI, workers finish ≤ 30 s for 100-page docs.  
4. **Interactive UX** – Modern, dark UI, accessible (WCAG-AA, reduced-motion).  
5. **Privacy-by-design** – Minimise retention; never resell data.

---

## Brand voice

**Friendly-hacker, concise, confident**.  
Headlines ≤ 60 chars, CTAs are verbs, avoid corporate fluff.

---

## Tech stack snapshot

| Layer | Choice |
|-------|--------|
| Frontend | Next.js 14 + Tailwind + shadcn/ui |
| Worker MVP | **Rust** + `pdfcpu-rs` or `lopdf` + `orsoc` OCR exploration |
| Future Worker | Go (Golang) + `pdfcpu` |
| Infra | AWS Free-tier: S3, SQS, Lambda (container for Rust), DynamoDB |
| DevOps | Terraform, GitHub Actions, Vercel (web) |
| Fonts | Inter (UI) + Reem Kufi Ink (logo) |
| Theme | Dark `#0D0D0D` bg, Surface `#18181B`, Accent `#FACC15` |

---

## Compliance trajectory

*Launch with privacy statement only.*  
Prepare GDPR-ready deletion/export hooks.  SOC-2 / HIPAA deferred until paid plans.

---

## Success metrics

* Hero CTA click-through ≥ 25 % (desktop).  
* Job success < 5 s for ≤ 25 pages, < 30 s for 100+ pages.  
* Zero file-retention incidents.  
* LCP ≤ 2.5 s; CLS ≤ 0.1.

---

## Agent playbook

1. **Assume PDF-first** whenever “docs” is ambiguous.  
2. Use Rust for all heavy processing code until the migration flag flips.  
3. Lean on open-source libraries; if cost emerges, propose free fallback first.  
4. Never expose internal roadmap labels (V1/V2/V3) publicly—describe the *final vision*.  
5. When choices clash, optimise for speed → privacy → cost → feature depth, in that order.  
6. Honor the design tokens below in every UI asset.

```json
{
  "colors": {
    "bg-dark": "#0D0D0D",
    "surface": "#18181B",
    "text": "#FFFFFF",
    "muted": "#A1A1AA",
    "primary": "#FACC15"
  },
  "radius": 12
}
