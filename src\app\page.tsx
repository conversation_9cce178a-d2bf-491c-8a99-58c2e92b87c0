import PdfUpload from '@/components/pdf-upload';
import { FileText, Zap, Shield, Clock } from 'lucide-react';

export default function Home() {
  return (
    <div className="min-h-screen bg-[#0D0D0D] text-white">
      {/* Header */}
      <header className="border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-3">
              <FileText className="h-8 w-8 text-[#FACC15]" />
              <h1 className="text-2xl font-bold" style={{ fontFamily: 'Reem <PERSON>, sans-serif' }}>
                dev-docs
              </h1>
            </div>
            <nav className="hidden md:flex space-x-8">
              <a href="#features" className="text-gray-300 hover:text-white transition-colors">
                Features
              </a>
              <a href="#about" className="text-gray-300 hover:text-white transition-colors">
                About
              </a>
              <a href="#pricing" className="text-gray-300 hover:text-white transition-colors">
                Pricing
              </a>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-8">
            <h2 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent">
              Your All-in-One
              <br />
              <span className="text-[#FACC15]">PDF Toolbox</span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Make every PDF task instant, free for core needs, and privacy-respecting.
              Merge, split, compress, and convert with blazing speed.
            </p>
          </div>

          {/* Feature Pills */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {[
              { icon: Zap, text: 'Lightning Fast' },
              { icon: Shield, text: 'Privacy First' },
              { icon: Clock, text: '2-Hour Auto-Delete' },
            ].map(({ icon: Icon, text }) => (
              <div
                key={text}
                className="flex items-center space-x-2 px-4 py-2 bg-gray-800/50 rounded-full border border-gray-700"
              >
                <Icon className="h-4 w-4 text-[#FACC15]" />
                <span className="text-sm font-medium">{text}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* PDF Upload Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-[#18181B]/30">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold mb-4">
              Start Processing Your PDFs
            </h3>
            <p className="text-lg text-gray-400 max-w-2xl mx-auto">
              Upload your PDF files and choose from our powerful processing options.
              All processing happens securely and files are automatically deleted after 2 hours.
            </p>
          </div>
          
          <PdfUpload />
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h3 className="text-3xl md:text-4xl font-bold mb-4">
              Everything You Need for PDFs
            </h3>
            <p className="text-lg text-gray-400 max-w-2xl mx-auto">
              Professional-grade PDF tools, completely free for core features
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                title: 'Smart Merge',
                description: 'Combine unlimited PDFs with intelligent page ordering',
                icon: '🔗',
              },
              {
                title: 'Precise Split',
                description: 'Extract specific pages or split into individual files',
                icon: '✂️',
              },
              {
                title: 'Compress',
                description: 'Reduce file sizes while maintaining quality',
                icon: '🗜️',
              },
              {
                title: 'Document Info',
                description: 'Get detailed metadata and document statistics',
                icon: '📊',
              },
            ].map((feature) => (
              <div
                key={feature.title}
                className="p-6 bg-gray-800/30 rounded-xl border border-gray-700 hover:border-gray-600 transition-all duration-200 hover:scale-105"
              >
                <div className="text-3xl mb-4">{feature.icon}</div>
                <h4 className="text-xl font-semibold mb-2">{feature.title}</h4>
                <p className="text-gray-400">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-4 sm:px-6 lg:px-8 bg-[#18181B]/30">
        <div className="max-w-4xl mx-auto text-center">
          <h3 className="text-3xl md:text-4xl font-bold mb-4">
            Simple, Transparent Pricing
          </h3>
          <p className="text-lg text-gray-400 mb-12">
            Core PDF operations are completely free. Advanced features coming soon.
          </p>

          <div className="bg-gray-800/50 rounded-2xl border border-gray-700 p-8 max-w-md mx-auto">
            <div className="mb-6">
              <h4 className="text-2xl font-bold mb-2">Free Forever</h4>
              <p className="text-4xl font-bold text-[#FACC15]">$0</p>
              <p className="text-gray-400 mt-2">For all core PDF operations</p>
            </div>

            <ul className="space-y-3 mb-8 text-left">
              {[
                'Unlimited PDF merging',
                'Page splitting & extraction',
                'File compression',
                'Document information',
                '2-hour auto-delete',
                'No watermarks',
              ].map((feature) => (
                <li key={feature} className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-[#FACC15] rounded-full" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>

            <div className="bg-[#FACC15]/10 border border-[#FACC15]/20 rounded-lg p-4">
              <p className="text-[#FACC15] font-semibold">Advanced Features Coming Soon</p>
              <p className="text-sm text-gray-400 mt-1">
                OCR, workflow automation, and more
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-gray-800 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-3 mb-4 md:mb-0">
              <FileText className="h-6 w-6 text-[#FACC15]" />
              <span className="text-lg font-semibold" style={{ fontFamily: 'Reem Kufi Ink, sans-serif' }}>
                dev-docs
              </span>
            </div>
            
            <div className="flex space-x-6 text-sm text-gray-400">
              <a href="#" className="hover:text-white transition-colors">
                Privacy Policy
              </a>
              <a href="#" className="hover:text-white transition-colors">
                Terms of Service
              </a>
              <a href="#" className="hover:text-white transition-colors">
                Open Source
              </a>
            </div>
          </div>
          
          <div className="mt-8 pt-8 border-t border-gray-800 text-center text-sm text-gray-500">
            <p>© 2024 dev-docs. All rights reserved. Built with privacy and performance in mind.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}